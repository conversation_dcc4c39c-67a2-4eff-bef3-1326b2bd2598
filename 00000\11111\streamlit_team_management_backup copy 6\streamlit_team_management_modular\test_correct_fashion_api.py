#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的换装API测试 - 使用真实的服装模板
Correct Fashion API Test - Using Real Clothing Templates
"""

import requests
import time
import os
from PIL import Image, ImageDraw, ImageFont
import io

# API配置
API_KEY = "sk-Y5BDQdWlSCr2nQUYvbeKhEIwzzzg5AzGq8nTFR7KArlQuM3o"
BASE_URL = "https://api.302.ai"

# 换装参数
FASHION_TRYON_CONFIG = {
    "modelImgSegLabels": "10",    # 10-上衣, 5-裤子, 6-裙子
    "clothesImgSegLabels": "10"   # 10-上衣, 5-裤子, 6-裙子
}

# 超时配置
TIMEOUT_CONFIG = {
    "request_timeout": 30,
    "task_check_interval": 30,
    "max_retry_attempts": 20
}

def create_realistic_clothing_template():
    """创建一个更真实的服装模板图片"""
    print("🎨 创建真实的服装模板图片...")
    
    os.makedirs("test_correct", exist_ok=True)
    
    # 创建一个更真实的T恤模板
    width, height = 512, 512
    img = Image.new('RGB', (width, height), 'white')
    draw = ImageDraw.Draw(img)
    
    # 绘制T恤轮廓
    # T恤主体
    shirt_color = (70, 130, 180)  # 钢蓝色
    
    # T恤主体矩形
    shirt_body = [
        (width//4, height//3),      # 左上
        (3*width//4, height//3),    # 右上
        (3*width//4, 4*height//5),  # 右下
        (width//4, 4*height//5)     # 左下
    ]
    draw.polygon(shirt_body, fill=shirt_color)
    
    # T恤袖子
    # 左袖子
    left_sleeve = [
        (width//6, height//3 + 20),
        (width//4, height//3),
        (width//4, height//2),
        (width//6, height//2 + 20)
    ]
    draw.polygon(left_sleeve, fill=shirt_color)
    
    # 右袖子
    right_sleeve = [
        (3*width//4, height//3),
        (5*width//6, height//3 + 20),
        (5*width//6, height//2 + 20),
        (3*width//4, height//2)
    ]
    draw.polygon(right_sleeve, fill=shirt_color)
    
    # T恤领口
    collar = [
        (width//2 - 30, height//3),
        (width//2 + 30, height//3),
        (width//2 + 20, height//3 + 30),
        (width//2 - 20, height//3 + 30)
    ]
    draw.polygon(collar, fill='white')
    
    # 添加一些细节
    # 胸前图案
    pattern_center = (width//2, height//2)
    draw.ellipse([
        pattern_center[0] - 40, pattern_center[1] - 20,
        pattern_center[0] + 40, pattern_center[1] + 20
    ], fill=(255, 255, 255), outline=(0, 0, 0), width=2)
    
    # 添加文字
    try:
        # 尝试使用系统字体
        font = ImageFont.truetype("arial.ttf", 16)
    except:
        # 如果没有找到字体，使用默认字体
        font = ImageFont.load_default()
    
    draw.text((width//2 - 20, height//2 - 5), "LOGO", fill=(0, 0, 0), font=font)
    
    # 保存图片
    clothes_path = "test_correct/realistic_tshirt.png"
    img.save(clothes_path)
    print(f"👕 创建服装模板: {clothes_path}")
    
    return clothes_path

def download_real_clothing_image():
    """尝试下载一个真实的服装图片"""
    print("📥 尝试获取真实的服装图片...")

    # 确保目录存在
    os.makedirs("test_correct", exist_ok=True)

    # 创建一个简单但更真实的服装图片
    # 这里我们创建一个看起来更像真实T恤的图片
    width, height = 512, 512
    img = Image.new('RGBA', (width, height), (255, 255, 255, 0))  # 透明背景
    draw = ImageDraw.Draw(img)
    
    # 绘制更真实的T恤
    # 使用渐变色效果
    shirt_color = (50, 150, 200)  # 蓝色T恤
    
    # T恤主体 - 更真实的形状
    points = [
        (150, 120),  # 左肩
        (200, 100),  # 左领
        (250, 100),  # 右领  
        (300, 120),  # 右肩
        (350, 140),  # 右袖
        (350, 200),  # 右袖底
        (320, 220),  # 右腋下
        (320, 450),  # 右下角
        (130, 450),  # 左下角
        (130, 220),  # 左腋下
        (100, 200),  # 左袖底
        (100, 140),  # 左袖
    ]
    
    draw.polygon(points, fill=shirt_color, outline=(30, 100, 150), width=3)
    
    # 添加领口
    collar_points = [
        (200, 100),
        (250, 100),
        (240, 130),
        (210, 130)
    ]
    draw.polygon(collar_points, fill=(255, 255, 255), outline=(30, 100, 150), width=2)
    
    # 添加一些纹理线条
    for i in range(5):
        y = 150 + i * 60
        draw.line([(140, y), (310, y)], fill=(40, 120, 180), width=1)
    
    # 保存图片
    clothes_path = "test_correct/real_tshirt.png"
    img.save(clothes_path)
    print(f"👕 创建真实T恤模板: {clothes_path}")
    
    return clothes_path

def test_with_real_clothing():
    """使用真实服装图片进行测试"""
    print("🧪 使用真实服装图片测试换装API")
    print("=" * 60)
    
    # 创建真实的服装图片
    clothes_image = download_real_clothing_image()
    
    # 查找球员照片
    player_image = None
    uploads_dir = "uploads"
    if os.path.exists(uploads_dir):
        for team_dir in os.listdir(uploads_dir):
            team_path = os.path.join(uploads_dir, team_dir)
            if os.path.isdir(team_path):
                for file in os.listdir(team_path):
                    if file.endswith('.jpg'):
                        player_image = os.path.join(team_path, file)
                        break
                if player_image:
                    break
    
    if not player_image:
        print("❌ 没有找到球员照片")
        return False
    
    print(f"📸 球员照片: {player_image}")
    print(f"👕 服装图片: {clothes_image}")
    
    # 执行换装API测试
    result = test_fashion_api(player_image, clothes_image)
    
    if result:
        print("\n🎉 真实服装换装测试成功！")
        print(f"📁 结果文件: {result}")
        
        # 检查文件大小
        if os.path.exists(result):
            file_size = os.path.getsize(result)
            print(f"📊 文件大小: {file_size} 字节")
            
            # 验证图片是否可以正常打开
            try:
                with Image.open(result) as img:
                    print(f"📏 图片尺寸: {img.size}")
                    print(f"🎨 图片模式: {img.mode}")
                print("✅ 结果图片验证通过")
            except Exception as e:
                print(f"❌ 结果图片验证失败: {e}")
        
        return True
    else:
        print("\n❌ 真实服装换装测试失败")
        return False

def test_fashion_api(model_image, clothes_image):
    """测试换装API"""
    print(f"\n🎯 开始换装API测试")
    print(f"📸 模特图片: {model_image}")
    print(f"👕 服装图片: {clothes_image}")
    
    if not os.path.exists(model_image):
        print(f"❌ 模特图片不存在: {model_image}")
        return None
    
    if not os.path.exists(clothes_image):
        print(f"❌ 服装图片不存在: {clothes_image}")
        return None
    
    url = f"{BASE_URL}/302/comfyui/clothes-changer/create-task"
    headers = {"Authorization": f"Bearer {API_KEY}"}
    
    try:
        with open(model_image, 'rb') as model_file, open(clothes_image, 'rb') as clothes_file:
            files = {
                'modelImageFile': (os.path.basename(model_image), model_file, 'image/jpeg'),
                'clothesImageFile': (os.path.basename(clothes_image), clothes_file, 'image/png')
            }
            
            data = FASHION_TRYON_CONFIG
            
            print("📤 发送换装任务请求...")
            response = requests.post(url, headers=headers, files=files, data=data, 
                                   timeout=TIMEOUT_CONFIG["request_timeout"])
            
    except Exception as e:
        print(f"❌ 请求失败: {e}")
        return None
    
    print(f"📊 响应状态码: {response.status_code}")
    
    if response.status_code in [200, 201]:
        try:
            result = response.json()
            print(f"📄 响应内容: {result}")
            
            if result.get('code') == 200 and 'data' in result:
                task_id = result['data']['taskId']
                print(f"✅ 换装任务创建成功！任务ID: {task_id}")
                return wait_for_task_completion(task_id)
            else:
                print(f"❌ 任务创建失败: {result}")
                return None
        except Exception as e:
            print(f"❌ 解析响应失败: {e}")
            return None
    else:
        print(f"❌ API请求失败: {response.status_code}")
        print(f"📄 响应内容: {response.text}")
        return None

def wait_for_task_completion(task_id):
    """等待任务完成"""
    print(f"⏳ 等待任务完成...")
    
    url = f"{BASE_URL}/302/comfyui/clothes-changer/check-task-status"
    headers = {"Authorization": f"Bearer {API_KEY}"}
    
    max_attempts = TIMEOUT_CONFIG["max_retry_attempts"]
    attempt = 0
    
    while attempt < max_attempts:
        attempt += 1
        print(f"🔄 第{attempt}次查询...")
        
        params = {"taskId": task_id}
        try:
            response = requests.get(url, headers=headers, params=params, 
                                  timeout=TIMEOUT_CONFIG["request_timeout"])
        except Exception as e:
            print(f"❌ 网络请求失败: {e}")
            time.sleep(TIMEOUT_CONFIG["task_check_interval"])
            continue
        
        if response.status_code == 200:
            try:
                result = response.json()
                status = result.get('data', 'UNKNOWN')
                
                print(f"📈 任务状态: {status}")
                
                if status == 'SUCCESS' and 'output' in result:
                    output = result['output']
                    result_url = output.get('resultUrl', '')
                    
                    print(f"🎉 换装任务完成！")
                    print(f"🔗 结果图URL: {result_url}")
                    
                    return download_result_image(result_url, "correct_fashion_result.png")
                    
                elif status in ['RUNNING', 'QUEUED', 'SUBMITTING']:
                    print(f"⏳ 任务进行中... 等待{TIMEOUT_CONFIG['task_check_interval']}秒后再次查询...")
                    time.sleep(TIMEOUT_CONFIG["task_check_interval"])
                    
                else:
                    print(f"❌ 任务失败，状态: {status}")
                    print(f"📄 完整响应: {result}")
                    return None
                    
            except Exception as e:
                print(f"❌ 解析响应失败: {e}")
                time.sleep(TIMEOUT_CONFIG["task_check_interval"])
                continue
        else:
            print(f"❌ 查询失败: {response.status_code}")
            time.sleep(TIMEOUT_CONFIG["task_check_interval"])
            continue
    
    print("⏰ 等待超时")
    return None

def download_result_image(url, filename):
    """下载结果图片"""
    try:
        print(f"📥 下载结果图片: {url}")
        response = requests.get(url, timeout=30)
        if response.status_code == 200:
            output_path = os.path.join("test_correct", filename)
            
            with open(output_path, 'wb') as f:
                f.write(response.content)
            
            print(f"✅ 图片下载成功: {output_path}")
            return output_path
        else:
            print(f"❌ 下载图片失败: {response.status_code}")
            return None
    except Exception as e:
        print(f"❌ 下载图片异常: {e}")
        return None

def main():
    """主测试函数"""
    print("🧪 正确的换装API测试")
    print("使用真实的服装模板图片")
    print("=" * 60)
    
    success = test_with_real_clothing()
    
    if success:
        print("\n🎉 测试完成！换装API工作正常")
        print("📋 测试要点:")
        print("  ✅ 使用了真实的服装模板图片")
        print("  ✅ API调用成功")
        print("  ✅ 任务状态正常")
        print("  ✅ 结果图片下载成功")
    else:
        print("\n❌ 测试失败，请检查:")
        print("  🔍 API密钥是否正确")
        print("  🔍 网络连接是否正常")
        print("  🔍 输入图片是否符合要求")

if __name__ == "__main__":
    main()
