#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增强聊天功能集成
验证AI聊天组件是否正确集成了函数调用功能
"""

import sys
import os
import json
from typing import Dict, Any

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

def test_chat_component_imports():
    """测试聊天组件导入"""
    print("🔍 测试聊天组件导入...")
    
    try:
        from components.ai_chat import AIChatComponent
        print("✅ AI聊天组件导入成功")
        
        # 测试新方法存在性
        component = AIChatComponent()
        
        assert hasattr(component, 'process_enhanced_message')
        assert hasattr(component, 'show_enhanced_features_status')
        assert hasattr(component, 'show_extracted_data_summary')
        print("✅ 新增方法存在")
        
        return True
    except Exception as e:
        print(f"❌ 聊天组件测试失败: {e}")
        return False

def test_enhanced_ai_service_functions():
    """测试增强AI服务函数"""
    print("\n🚀 测试增强AI服务函数...")
    
    try:
        from services.enhanced_ai_service import EnhancedAIService
        
        service = EnhancedAIService("test_user")
        
        # 测试球队信息提取函数
        team_args = {
            "extracted_info": {
                "basic_info": {
                    "team_name": "蓝鹰足球俱乐部",
                    "contact_person": "张三",
                    "contact_phone": "13800138000"
                },
                "management": {
                    "coach_name": "李四"
                },
                "competition": {
                    "competition_name": "2024年淄川市五人制足球联赛"
                }
            },
            "confidence": 0.95
        }
        
        result = service._extract_team_info(team_args)
        assert result.get("success") == True
        assert "蓝鹰足球俱乐部" in str(result)
        print("✅ 球队信息提取函数正常")
        
        # 测试球员信息提取函数
        player_args = {
            "players": [
                {
                    "basic_info": {
                        "name": "张三",
                        "jersey_number": "10",
                        "position": "前锋"
                    },
                    "contact_info": {
                        "phone": "13800138000"
                    }
                }
            ],
            "confidence": 0.90
        }
        
        result = service._extract_player_info(player_args)
        assert result.get("success") == True
        assert result.get("players_count") == 1
        print("✅ 球员信息提取函数正常")
        
        return True
    except Exception as e:
        print(f"❌ 增强AI服务函数测试失败: {e}")
        return False

def test_ai_service_enhanced_methods():
    """测试AI服务增强方法"""
    print("\n🤖 测试AI服务增强方法...")
    
    try:
        from services.ai_service import AIService
        
        service = AIService("test_user")
        
        # 测试增强功能检查
        has_enhanced = service.has_enhanced_features()
        print(f"✅ 增强功能检查: {'可用' if has_enhanced else '不可用'}")
        
        # 测试新方法存在性
        assert hasattr(service, 'generate_enhanced_response')
        assert hasattr(service, 'extract_team_info_from_text')
        assert hasattr(service, 'extract_player_info_from_text')
        print("✅ 增强方法存在")
        
        return True
    except Exception as e:
        print(f"❌ AI服务增强方法测试失败: {e}")
        return False

def test_conversation_flow_simulation():
    """模拟对话流程测试"""
    print("\n💬 模拟对话流程测试...")
    
    try:
        from components.ai_chat import AIChatComponent
        
        component = AIChatComponent()
        
        # 模拟用户输入
        test_message = "我们是蓝鹰足球俱乐部，联系人是张三，电话13800138000。教练是李四，要参加2024年淄川市五人制足球联赛。"
        test_team = "测试球队"
        
        print(f"📝 模拟用户输入: {test_message[:50]}...")
        
        # 测试消息处理（不实际调用OpenAI API）
        if component.ai_service.has_enhanced_features():
            print("✅ 增强功能可用，可以处理函数调用")
        else:
            print("ℹ️ 使用基础功能")
        
        print("✅ 对话流程模拟完成")
        
        return True
    except Exception as e:
        print(f"❌ 对话流程测试失败: {e}")
        return False

def test_function_definitions_compatibility():
    """测试函数定义兼容性"""
    print("\n⚙️ 测试函数定义兼容性...")
    
    try:
        from config.ai_schemas import FUNCTION_DEFINITIONS
        
        # 检查必需的函数
        function_names = [func["function"]["name"] for func in FUNCTION_DEFINITIONS]
        
        required_functions = [
            "extract_team_info",
            "extract_player_info",
            "validate_team_data",
            "generate_team_suggestions"
        ]
        
        for func_name in required_functions:
            if func_name in function_names:
                print(f"✅ 函数 {func_name} 已定义")
            else:
                print(f"❌ 函数 {func_name} 缺失")
                return False
        
        # 检查函数参数结构
        for func_def in FUNCTION_DEFINITIONS:
            func_info = func_def["function"]
            assert "name" in func_info
            assert "description" in func_info
            assert "parameters" in func_info
            assert "type" in func_info["parameters"]
            assert "properties" in func_info["parameters"]
        
        print("✅ 函数定义结构正确")
        
        return True
    except Exception as e:
        print(f"❌ 函数定义兼容性测试失败: {e}")
        return False

def print_usage_guide():
    """打印使用指南"""
    print("\n" + "="*60)
    print("🎯 增强AI聊天功能使用指南")
    print("="*60)
    
    print("""
🚀 新功能特性:
  • AI可以自动提取和保存球队信息
  • AI可以自动提取和保存球员信息
  • 支持结构化输出验证
  • 智能对话引导用户补充信息

💬 使用示例:
  用户: "我们是蓝鹰足球俱乐部，联系人是张三，电话13800138000。
        教练是李四，要参加2024年淄川市五人制足球联赛。"
  
  AI: "好的，非常感谢您提供的信息！
      我已确认以下这些信息：
      • 俱乐部名称: 蓝鹰足球俱乐部
      • 联系人: 张三
      • 联系电话: 13800138000
      • 教练: 李四
      • 比赛名称: 2024年淄川市五人制足球联赛
      
      [AI自动调用函数保存信息]
      
      接下来的信息收集：
      请问比赛的具体时间是什么时候呢？"

🔧 技术特性:
  • 自动函数调用: AI主动保存数据
  • 结构化验证: 确保数据格式正确
  • 渐进式收集: 逐步引导用户补充信息
  • 智能推断: 从自然语言中提取结构化数据

📊 数据管理:
  • 提取的信息自动保存到session state
  • 支持球队和球员信息分类管理
  • 提供数据完整性验证
  • 显示提取结果和置信度
""")

def main():
    """主测试函数"""
    print("🧪 增强AI聊天功能集成测试")
    print("="*50)
    
    tests = [
        ("聊天组件导入", test_chat_component_imports),
        ("增强AI服务函数", test_enhanced_ai_service_functions),
        ("AI服务增强方法", test_ai_service_enhanced_methods),
        ("对话流程模拟", test_conversation_flow_simulation),
        ("函数定义兼容性", test_function_definitions_compatibility)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！增强AI聊天功能集成成功！")
        print_usage_guide()
    else:
        print("⚠️ 部分测试失败，请检查集成问题")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
