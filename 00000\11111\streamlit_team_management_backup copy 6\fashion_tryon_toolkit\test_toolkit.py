#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时尚换装工具包测试脚本
Fashion Try-On Toolkit Test Script

用于验证工具包是否正确安装和配置
"""

import os
import sys
from pathlib import Path
import time

def test_imports():
    """测试模块导入"""
    print("🔍 测试模块导入...")
    
    try:
        import requests
        print("✅ requests 导入成功")
    except ImportError as e:
        print(f"❌ requests 导入失败: {e}")
        return False
    
    try:
        from PIL import Image
        print("✅ PIL 导入成功")
    except ImportError as e:
        print(f"❌ PIL 导入失败: {e}")
        return False
    
    try:
        import numpy
        print("✅ numpy 导入成功")
    except ImportError as e:
        print(f"❌ numpy 导入失败: {e}")
        return False
    
    try:
        import config
        print("✅ config 导入成功")
    except ImportError as e:
        print(f"❌ config 导入失败: {e}")
        return False
    
    return True

def test_config():
    """测试配置"""
    print("\n⚙️ 测试配置...")
    
    try:
        from config import validate_config, API_KEY, BASE_URL
        
        # 检查API密钥
        if not API_KEY or API_KEY in ["your-api-key-here", ""]:
            print("❌ API密钥未设置")
            return False
        
        print(f"✅ API密钥已设置: {API_KEY[:10]}...")
        print(f"✅ 基础URL: {BASE_URL}")
        
        # 验证配置
        validate_config()
        print("✅ 配置验证通过")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置测试失败: {e}")
        return False

def test_directories():
    """测试目录创建"""
    print("\n📁 测试目录...")
    
    try:
        from config import OUTPUT_DIRS
        
        for name, path in OUTPUT_DIRS.items():
            if os.path.exists(path):
                print(f"✅ 目录存在: {name} -> {path}")
            else:
                print(f"⚠️  目录不存在，正在创建: {name} -> {path}")
                os.makedirs(path, exist_ok=True)
                print(f"✅ 目录已创建: {path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 目录测试失败: {e}")
        return False

def test_image_processing():
    """测试图片处理功能"""
    print("\n🖼️ 测试图片处理...")
    
    try:
        from PIL import Image
        import numpy as np
        
        # 创建测试图片
        test_image = Image.new('RGB', (100, 100), color='red')
        test_path = "test_image.jpg"
        test_image.save(test_path)
        print("✅ 测试图片创建成功")
        
        # 测试图片读取
        loaded_image = Image.open(test_path)
        print(f"✅ 图片读取成功: {loaded_image.size}")
        
        # 测试图片转换
        rgba_image = loaded_image.convert("RGBA")
        print("✅ 图片格式转换成功")
        
        # 测试白底合成
        background = Image.new('RGB', (100, 100), 'white')
        background.paste(rgba_image, (0, 0), rgba_image)
        print("✅ 图片合成测试成功")
        
        # 清理测试文件
        os.remove(test_path)
        print("✅ 测试文件清理完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 图片处理测试失败: {e}")
        return False

def test_network_connectivity():
    """测试网络连接"""
    print("\n🌐 测试网络连接...")
    
    try:
        import requests
        from config import BASE_URL, TIMEOUT_CONFIG
        
        # 测试基础连接
        response = requests.get(BASE_URL, timeout=TIMEOUT_CONFIG["request_timeout"])
        print(f"✅ 网络连接正常: {response.status_code}")
        
        return True
        
    except requests.exceptions.Timeout:
        print("❌ 网络连接超时")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 网络连接失败")
        return False
    except Exception as e:
        print(f"❌ 网络测试失败: {e}")
        return False

def test_cost_calculation():
    """测试成本计算"""
    print("\n💰 测试成本计算...")
    
    try:
        from config import get_total_cost_per_image, get_total_cost_cny_per_image, API_COSTS
        
        # 测试成本计算
        cost_ptc = get_total_cost_per_image()
        cost_cny = get_total_cost_cny_per_image()
        
        print(f"✅ 单张图片成本: {cost_ptc} PTC")
        print(f"✅ 人民币成本: {cost_cny:.1f} 元")
        
        # 验证成本组成
        expected_cost = sum(API_COSTS.values())
        if abs(cost_ptc - expected_cost) < 0.001:
            print("✅ 成本计算正确")
            return True
        else:
            print(f"❌ 成本计算错误: 期望{expected_cost}, 实际{cost_ptc}")
            return False
        
    except Exception as e:
        print(f"❌ 成本计算测试失败: {e}")
        return False

def test_file_operations():
    """测试文件操作"""
    print("\n📄 测试文件操作...")
    
    try:
        # 测试文件创建和读取
        test_content = "测试内容"
        test_file = "test_file.txt"
        
        with open(test_file, 'w', encoding='utf-8') as f:
            f.write(test_content)
        print("✅ 文件写入成功")
        
        with open(test_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if content == test_content:
            print("✅ 文件读取成功")
        else:
            print("❌ 文件内容不匹配")
            return False
        
        # 清理测试文件
        os.remove(test_file)
        print("✅ 文件操作测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件操作测试失败: {e}")
        return False

def run_comprehensive_test():
    """运行综合测试"""
    print("🧪 时尚换装工具包综合测试")
    print("="*50)
    
    tests = [
        ("模块导入", test_imports),
        ("配置验证", test_config),
        ("目录检查", test_directories),
        ("图片处理", test_image_processing),
        ("网络连接", test_network_connectivity),
        ("成本计算", test_cost_calculation),
        ("文件操作", test_file_operations)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 显示测试结果
    print("\n" + "="*50)
    print("📊 测试结果汇总")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name:12} : {status}")
        if result:
            passed += 1
    
    print(f"\n总体结果: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！工具包已准备就绪。")
        print("\n📚 接下来您可以:")
        print("1. 查看 README.md 了解工具包概述")
        print("2. 查看 EXAMPLES.md 学习使用方法")
        print("3. 运行 single_fashion_tryon.py 处理单张照片")
        print("4. 运行 batch_fashion_tryon.py 批量处理照片")
        return True
    else:
        print(f"\n⚠️  {total-passed} 个测试失败，请检查配置和环境。")
        print("\n🔧 可能的解决方案:")
        print("1. 运行 python setup.py 重新安装")
        print("2. 检查 config.py 中的API密钥设置")
        print("3. 确保网络连接正常")
        print("4. 检查Python版本 (需要3.7+)")
        return False

def main():
    """主函数"""
    try:
        success = run_comprehensive_test()
        return success
    except KeyboardInterrupt:
        print("\n\n⚠️  测试被用户中断")
        return False
    except Exception as e:
        print(f"\n\n❌ 测试过程发生异常: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
