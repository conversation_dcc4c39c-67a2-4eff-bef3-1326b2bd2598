#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
常量定义
Constants Definition

定义应用程序中使用的所有常量
"""

from typing import Dict, Any, List


class ProcessOptions:
    """照片处理选项常量"""
    
    OPTIONS: Dict[str, Dict[str, Any]] = {
        "不处理": {
            "label": "🚫 不处理",
            "description": "跳过此球员，不进行任何处理",
            "fashion": False,
            "background": False,
            "white": False
        },
        "仅白底": {
            "label": "⚪ 仅白底",
            "description": "只添加白色背景",
            "fashion": False,
            "background": False,
            "white": True
        },
        "仅背景去除": {
            "label": "🖼️ 仅背景去除",
            "description": "只移除照片背景",
            "fashion": False,
            "background": True,
            "white": False
        },
        "仅换装": {
            "label": "🔄 仅换装",
            "description": "只进行AI换装（需要模板图）",
            "fashion": True,
            "background": False,
            "white": False
        },
        "背景去除+白底": {
            "label": "🖼️⚪ 背景去除+白底",
            "description": "去除背景后添加白底",
            "fashion": False,
            "background": True,
            "white": True
        },
        "换装+白底": {
            "label": "🔄⚪ 换装+白底",
            "description": "换装后添加白底",
            "fashion": True,
            "background": False,
            "white": True
        },
        "换装+背景去除": {
            "label": "🔄🖼️ 换装+背景去除",
            "description": "换装后去除背景",
            "fashion": True,
            "background": True,
            "white": False
        },
        "全套处理": {
            "label": "🔄🖼️⚪ 全套处理",
            "description": "换装+背景去除+白底",
            "fashion": True,
            "background": True,
            "white": True
        }
    }
    
    @classmethod
    def get_option_keys(cls) -> List[str]:
        """获取所有选项键"""
        return list(cls.OPTIONS.keys())
    
    @classmethod
    def get_option(cls, key: str) -> Dict[str, Any]:
        """获取指定选项"""
        return cls.OPTIONS.get(key, cls.OPTIONS["不处理"])
    
    @classmethod
    def needs_template(cls, key: str) -> bool:
        """检查选项是否需要模板图"""
        return cls.get_option(key).get("fashion", False)


class FileTypes:
    """文件类型常量"""
    
    SUPPORTED_IMAGE_TYPES: List[str] = ['png', 'jpg', 'jpeg', 'gif', 'bmp']
    SUPPORTED_TEMPLATE_TYPES: List[str] = ['png', 'jpg', 'jpeg']
    
    IMAGE_CONTENT_TYPES: Dict[str, str] = {
        'png': 'image/png',
        'jpg': 'image/jpeg',
        'jpeg': 'image/jpeg',
        'gif': 'image/gif',
        'bmp': 'image/bmp'
    }


class UIConstants:
    """UI界面常量"""
    
    # 批量操作按钮文本
    BATCH_BUTTONS = {
        "full_process": "🔄🖼️⚪ 全部设为全套处理",
        "bg_white": "🖼️⚪ 全部设为背景去除+白底", 
        "white_only": "⚪ 全部设为仅白底",
        "no_process": "🚫 全部设为不处理"
    }
    
    # 状态消息
    STATUS_MESSAGES = {
        "success_add": "✅ 球员 {name} (#{number}) 添加成功！",
        "success_delete": "✅ 球员 {name} 删除成功！",
        "success_team_create": "✅ 球队 \"{name}\" 创建成功！",
        "error_duplicate_number": "❌ 球衣号码 {number} 已被使用",
        "error_missing_fields": "❌ 姓名和球衣号码是必填项",
        "error_team_exists": "❌ 球队名称已存在",
        "error_save_failed": "❌ 保存数据失败",
        "error_player_not_found": "❌ 球员不存在",
        "error_photo_not_found": "❌ 照片不存在"
    }
    
    # 帮助文本
    HELP_TEXTS = {
        "file_upload": "支持 PNG, JPG, JPEG, GIF, BMP 格式",
        "template_upload": "用于AI换装的模板图片，系统会将球员照片中的服装替换为此模板样式",
        "jersey_number": "已使用号码: {used_numbers}",
        "english_ui": """
        📝 <strong>英文界面说明：</strong><br>
        • "Drag and drop file here" = 拖拽文件到此处<br>
        • "Browse files" = 浏览文件<br>
        • "Choose File" = 选择文件<br>
        • "Limit 200MB per file" = 单个文件大小限制200MB
        """
    }
    
    # 默认值
    DEFAULTS = {
        "team_name": "default",
        "team_display_name": "默认球队",
        "process_option": "全套处理",
        "jersey_number_start": 1
    }
