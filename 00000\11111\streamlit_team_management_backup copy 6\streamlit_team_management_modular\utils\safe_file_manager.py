#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
安全文件管理器
Safe File Manager

大公司级别的文件管理工具，提供健壮的文件操作和错误处理
"""

import os
import logging
import streamlit as st
from typing import Optional, Any, Union
from pathlib import Path
import hashlib
import time

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class SafeFileManager:
    """安全文件管理器 - 大公司级别的文件操作工具"""
    
    def __init__(self):
        self.default_image_path = "assets/default_player.png"  # 默认图片路径
    
    def safe_file_exists(self, file_path: Union[str, Path]) -> bool:
        """
        安全检查文件是否存在
        
        Args:
            file_path: 文件路径
            
        Returns:
            bool: 文件是否存在
        """
        try:
            if not file_path:
                return False
            return os.path.exists(str(file_path))
        except Exception as e:
            logger.warning(f"检查文件存在性时出错: {file_path}, 错误: {e}")
            return False
    
    def safe_image_display(self, image_source: Any, caption: str = "", width: Optional[int] = None, 
                          fallback_message: str = "图片暂时无法显示") -> bool:
        """
        安全显示图片，自动处理文件不存在的情况
        
        Args:
            image_source: 图片源（文件路径、文件对象等）
            caption: 图片标题
            width: 图片宽度
            fallback_message: 失败时的提示信息
            
        Returns:
            bool: 是否成功显示
        """
        try:
            # 尝试显示图片
            st.image(image_source, caption=caption, width=width)
            return True
        except Exception as e:
            # 记录错误但不中断程序
            logger.warning(f"显示图片失败: {e}")
            
            # 尝试显示默认图片
            if self.safe_file_exists(self.default_image_path):
                try:
                    st.image(self.default_image_path, caption=f"{caption} (默认图片)", width=width)
                    return True
                except Exception:
                    pass
            
            # 显示友好的错误信息
            st.warning(f"📷 {fallback_message}")
            if caption:
                st.caption(caption)
            return False
    
    def safe_file_read(self, file_path: Union[str, Path], encoding: str = 'utf-8') -> Optional[str]:
        """
        安全读取文件内容
        
        Args:
            file_path: 文件路径
            encoding: 文件编码
            
        Returns:
            Optional[str]: 文件内容，失败时返回None
        """
        try:
            if not self.safe_file_exists(file_path):
                logger.warning(f"文件不存在: {file_path}")
                return None
                
            with open(file_path, 'r', encoding=encoding) as f:
                return f.read()
        except Exception as e:
            logger.error(f"读取文件失败: {file_path}, 错误: {e}")
            return None
    
    def safe_file_write(self, file_path: Union[str, Path], content: str, 
                       encoding: str = 'utf-8', create_dirs: bool = True) -> bool:
        """
        安全写入文件
        
        Args:
            file_path: 文件路径
            content: 文件内容
            encoding: 文件编码
            create_dirs: 是否自动创建目录
            
        Returns:
            bool: 是否成功
        """
        try:
            file_path = Path(file_path)
            
            # 创建目录
            if create_dirs:
                file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 写入文件
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
            
            logger.info(f"文件写入成功: {file_path}")
            return True
        except Exception as e:
            logger.error(f"文件写入失败: {file_path}, 错误: {e}")
            return False
    
    def generate_safe_filename(self, original_name: str, prefix: str = "") -> str:
        """
        生成安全的文件名
        
        Args:
            original_name: 原始文件名
            prefix: 文件名前缀
            
        Returns:
            str: 安全的文件名
        """
        try:
            # 获取文件扩展名
            name, ext = os.path.splitext(original_name)
            
            # 生成时间戳和哈希
            timestamp = str(int(time.time()))
            hash_obj = hashlib.md5(f"{name}{timestamp}".encode())
            hash_str = hash_obj.hexdigest()[:8]
            
            # 组合安全文件名
            safe_name = f"{prefix}{timestamp}_{hash_str}{ext}"
            return safe_name
        except Exception as e:
            logger.error(f"生成安全文件名失败: {original_name}, 错误: {e}")
            return f"file_{int(time.time())}.jpg"
    
    def cleanup_invalid_references(self, data: Any) -> Any:
        """
        清理数据中的无效文件引用
        
        Args:
            data: 要清理的数据
            
        Returns:
            Any: 清理后的数据
        """
        try:
            # 这里可以添加更复杂的清理逻辑
            # 目前简单返回原数据
            return data
        except Exception as e:
            logger.error(f"清理无效引用失败: {e}")
            return data
    
    def handle_media_file_error(self, error: Exception, context: str = "") -> None:
        """
        处理媒体文件错误
        
        Args:
            error: 错误对象
            context: 错误上下文
        """
        error_msg = str(error)
        
        # 检查是否是媒体文件存储错误
        if "MediaFileStorageError" in error_msg or "No media file with id" in error_msg:
            logger.warning(f"检测到媒体文件存储错误: {context}")
            
            # 清理相关缓存
            try:
                st.cache_data.clear()
                logger.info("已清理缓存以解决媒体文件错误")
            except Exception as cache_error:
                logger.error(f"清理缓存失败: {cache_error}")
            
            # 显示用户友好的错误信息
            st.warning("🔄 检测到文件引用问题，已自动清理。请刷新页面继续使用。")
        else:
            # 其他类型的错误
            logger.error(f"文件操作错误: {context}, 错误: {error}")


# 全局实例
safe_file_manager = SafeFileManager()
