#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试真实的换装API效果
Test Real Fashion API Effects
"""

import os
import sys
import shutil
from pathlib import Path

# 添加当前目录到路径
sys.path.insert(0, '.')

def setup_test_environment():
    """设置测试环境"""
    print("🔧 设置测试环境...")
    
    # 创建必要的目录
    os.makedirs("temp_files", exist_ok=True)
    os.makedirs("test_images", exist_ok=True)
    
    # 复制测试图片
    source_dir = Path("../fashion_tryon_toolkit/test_photos")
    target_dir = Path("test_images")
    
    if source_dir.exists():
        for img_file in source_dir.glob("*.jpg"):
            target_file = target_dir / img_file.name
            if not target_file.exists():
                shutil.copy2(img_file, target_file)
                print(f"📋 复制测试图片: {img_file.name}")
    
    # 查找现有的球员照片
    player_images = []
    
    # 从uploads目录查找
    uploads_dir = Path("uploads")
    if uploads_dir.exists():
        for team_dir in uploads_dir.iterdir():
            if team_dir.is_dir():
                for img_file in team_dir.glob("*.jpg"):
                    player_images.append(str(img_file))
                    if len(player_images) >= 2:  # 只取前2张用于测试
                        break
            if len(player_images) >= 2:
                break
    
    # 从test_images目录查找
    if len(player_images) < 2:
        for img_file in target_dir.glob("*.jpg"):
            player_images.append(str(img_file))
            if len(player_images) >= 2:
                break
    
    print(f"📸 找到 {len(player_images)} 张测试图片")
    for img in player_images:
        print(f"  - {img}")
    
    return player_images

def test_fashion_api_service():
    """测试换装API服务"""
    print("\n🧪 测试换装API服务...")
    
    try:
        from services.fashion_api_service import fashion_api_service
        
        print(f"✅ 换装API服务可用性: {fashion_api_service.is_available()}")
        print(f"📡 API基础URL: {fashion_api_service.base_url}")
        print(f"🔑 API密钥: {fashion_api_service.api_key[:20]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 换装API服务测试失败: {e}")
        return False

def test_single_fashion_tryon(player_images):
    """测试单张照片换装"""
    if len(player_images) < 1:
        print("❌ 没有足够的测试图片")
        return False
    
    print("\n🎯 测试单张照片换装...")
    
    try:
        from services.fashion_api_service import fashion_api_service
        
        # 使用第一张图片作为模特图片
        model_image = player_images[0]
        
        # 创建一个简单的服装图片（纯色矩形）
        from PIL import Image
        clothes_image_path = "test_images/test_clothes.png"
        
        # 创建一个蓝色上衣图片
        clothes_img = Image.new('RGB', (512, 512), color='blue')
        clothes_img.save(clothes_image_path)
        
        print(f"📸 模特图片: {model_image}")
        print(f"👕 服装图片: {clothes_image_path}")
        
        # 执行换装
        result = fashion_api_service.process_single_complete_workflow(
            model_image, clothes_image_path
        )
        
        print(f"📊 处理结果: {result}")
        
        if result["success"]:
            print("🎉 单张照片换装测试成功！")
            print(f"📁 结果文件: {result['final_result']}")
            return True
        else:
            print(f"❌ 单张照片换装测试失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 单张照片换装测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_batch_fashion_tryon(player_images):
    """测试批量换装"""
    if len(player_images) < 2:
        print("❌ 没有足够的测试图片进行批量测试")
        return False
    
    print("\n🚀 测试批量换装...")
    
    try:
        from services.fashion_api_service import fashion_api_service
        
        # 创建服装图片
        from PIL import Image
        clothes_image_path = "test_images/test_clothes_batch.png"
        
        # 创建一个红色上衣图片
        clothes_img = Image.new('RGB', (512, 512), color='red')
        clothes_img.save(clothes_image_path)
        
        print(f"📸 球员图片数量: {len(player_images)}")
        print(f"👕 服装图片: {clothes_image_path}")
        
        # 执行批量换装
        result = fashion_api_service.process_batch_fashion_tryon(
            player_images[:2], clothes_image_path  # 只测试前2张
        )
        
        print(f"📊 批量处理结果: {result}")
        
        if result["success"]:
            print("🎉 批量换装测试成功！")
            print(f"📈 成功数量: {result['successful_count']}")
            print(f"📉 失败数量: {result['failed_count']}")
            return True
        else:
            print(f"❌ 批量换装测试失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ 批量换装测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ai_service_integration(player_images):
    """测试AI服务集成"""
    if len(player_images) < 1:
        print("❌ 没有足够的测试图片")
        return False
    
    print("\n🤖 测试AI服务集成...")
    
    try:
        from services.ai_service import AIService
        
        ai_service = AIService()
        
        print(f"✅ AI服务可用性: {ai_service.is_available()}")
        print(f"✅ 换装功能可用性: {ai_service.is_fashion_tryon_available()}")
        
        # 创建服装图片
        from PIL import Image
        clothes_image_path = "test_images/test_clothes_ai.png"
        
        # 创建一个绿色上衣图片
        clothes_img = Image.new('RGB', (512, 512), color='green')
        clothes_img.save(clothes_image_path)
        
        # 测试单张换装
        result = ai_service.fashion_tryon_single(
            player_images[0], clothes_image_path
        )
        
        print(f"📊 AI服务换装结果: {result}")
        
        if result["success"]:
            print("🎉 AI服务集成测试成功！")
            return True
        else:
            print(f"❌ AI服务集成测试失败: {result.get('error')}")
            return False
            
    except Exception as e:
        print(f"❌ AI服务集成测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🧪 开始真实换装API测试")
    print("=" * 60)
    
    # 设置测试环境
    player_images = setup_test_environment()
    
    if not player_images:
        print("❌ 没有找到测试图片，无法进行测试")
        return
    
    # 测试结果
    results = []
    
    # 1. 测试换装API服务
    results.append(("换装API服务", test_fashion_api_service()))
    
    # 2. 测试单张照片换装
    results.append(("单张照片换装", test_single_fashion_tryon(player_images)))
    
    # 3. 测试批量换装
    results.append(("批量换装", test_batch_fashion_tryon(player_images)))
    
    # 4. 测试AI服务集成
    results.append(("AI服务集成", test_ai_service_integration(player_images)))
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{test_name}: {status}")
    
    total_tests = len(results)
    passed_tests = sum(1 for _, success in results if success)
    
    print(f"\n📈 总体结果: {passed_tests}/{total_tests} 项测试通过")
    
    if passed_tests == total_tests:
        print("🎉 所有测试通过！换装API集成成功！")
    else:
        print("⚠️ 部分测试失败，请检查配置和网络连接")

if __name__ == "__main__":
    main()
