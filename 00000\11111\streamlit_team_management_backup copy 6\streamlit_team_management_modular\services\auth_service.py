#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户认证服务
User Authentication Service

实现简化的密码认证和用户数据隔离
"""

import hashlib
import os
import time
import json
import streamlit as st
from typing import Optional, <PERSON>ple
from datetime import datetime, timedelta
import streamlit.components.v1 as components


class AuthService:
    """用户认证服务"""
    
    def __init__(self):
        self.session_timeout = 7 * 24 * 3600  # 7天会话超时
        self.max_attempts = 5  # 最大尝试次数
        self.lockout_time = 15 * 60  # 锁定时间15分钟
        self.storage_key = "streamlit_team_auth"  # localStorage键名

        # 在初始化时尝试从localStorage恢复认证状态
        self._restore_auth_from_storage()
    
    @st.cache_data
    def generate_user_id(_self, password: str) -> str:
        """
        从密码生成用户ID（缓存优化）

        Args:
            password: 用户密码

        Returns:
            str: 用户ID
        """
        # 使用SHA256哈希生成用户ID
        hash_object = hashlib.sha256(password.encode('utf-8'))
        return f"user_{hash_object.hexdigest()[:12]}"
    
    def validate_password(self, password: str) -> Tuple[bool, str]:
        """
        验证密码格式
        
        Args:
            password: 用户输入的密码
            
        Returns:
            Tuple[bool, str]: (是否有效, 错误信息)
        """
        if not password:
            return False, "密码不能为空"
        
        if len(password) < 4:
            return False, "密码至少需要4个字符"
        
        if len(password) > 20:
            return False, "密码不能超过20个字符"
        
        return True, ""
    
    def login(self, password: str) -> Tuple[bool, str]:
        """
        用户登录
        
        Args:
            password: 用户密码
            
        Returns:
            Tuple[bool, str]: (是否成功, 消息)
        """
        # 验证密码格式
        is_valid, error_msg = self.validate_password(password)
        if not is_valid:
            return False, error_msg
        
        # 检查是否被锁定
        if self._is_locked_out():
            return False, f"尝试次数过多，请{self.lockout_time//60}分钟后再试"
        
        # 生成用户ID
        user_id = self.generate_user_id(password)
        
        # 设置会话
        self._set_session(user_id, password)

        # 保存认证状态到localStorage
        login_time = time.time()
        password_hint = self._generate_password_hint(password)
        self._save_auth_to_storage(user_id, password_hint, login_time)

        # 清除失败尝试记录
        self._clear_failed_attempts()

        return True, "登录成功"
    
    def logout(self) -> None:
        """用户登出"""
        keys_to_clear = [
            'user_id',
            'user_password_hint',
            'login_time',
            'current_team',
            'auth_restored'
        ]

        for key in keys_to_clear:
            if key in st.session_state:
                del st.session_state[key]

        # 清除localStorage中的认证数据
        self._clear_auth_storage()
    
    def is_logged_in(self) -> bool:
        """检查用户是否已登录"""
        # 首先尝试从存储恢复认证状态
        self._restore_auth_from_storage()

        if 'user_id' not in st.session_state:
            return False

        if 'login_time' not in st.session_state:
            return False

        # 检查会话是否过期
        login_time = st.session_state.login_time
        if time.time() - login_time > self.session_timeout:
            self.logout()
            return False

        return True
    
    def get_current_user_id(self) -> str:
        """获取当前用户ID"""
        if self.is_logged_in():
            return str(st.session_state.user_id)
        # 在测试环境或未登录时返回默认用户ID
        return "default_user"
    
    def get_user_display_name(self) -> str:
        """获取用户显示名称"""
        if 'user_password_hint' in st.session_state:
            hint = st.session_state.user_password_hint
            if len(hint) > 6:
                return f"{hint[:3]}***{hint[-1]}"
            else:
                return f"{hint[:2]}***"
        return "用户***"
    
    def set_password_hint(self, password: str, hint: str = "") -> None:
        """设置密码提示"""
        if not hint:
            # 自动生成提示
            if len(password) > 6:
                hint = f"{password[:3]}***{password[-1]}"
            else:
                hint = f"{password[:2]}***"
        
        st.session_state.user_password_hint = hint

    def _generate_password_hint(self, password: str) -> str:
        """生成密码提示"""
        if len(password) > 6:
            return f"{password[:3]}***{password[-1]}"
        else:
            return f"{password[:2]}***"
    
    def _set_session(self, user_id: str, password: str) -> None:
        """设置用户会话"""
        st.session_state.user_id = user_id
        st.session_state.login_time = time.time()
        
        # 设置密码提示
        self.set_password_hint(password)
    
    def _is_locked_out(self) -> bool:
        """检查是否被锁定"""
        if 'failed_attempts' not in st.session_state:
            return False
        
        failed_attempts = st.session_state.failed_attempts
        if len(failed_attempts) < self.max_attempts:
            return False
        
        # 检查最近的失败时间
        recent_failures = [
            attempt for attempt in failed_attempts
            if time.time() - attempt < self.lockout_time
        ]
        
        return len(recent_failures) >= self.max_attempts
    
    def _record_failed_attempt(self) -> None:
        """记录失败尝试"""
        if 'failed_attempts' not in st.session_state:
            st.session_state.failed_attempts = []
        
        st.session_state.failed_attempts.append(time.time())
        
        # 只保留最近的尝试记录
        cutoff_time = time.time() - self.lockout_time
        st.session_state.failed_attempts = [
            attempt for attempt in st.session_state.failed_attempts
            if attempt > cutoff_time
        ]
    
    def _clear_failed_attempts(self) -> None:
        """清除失败尝试记录"""
        if 'failed_attempts' in st.session_state:
            del st.session_state.failed_attempts
    
    def create_user_data_folder(self, user_id: str) -> str:
        """
        创建用户数据文件夹
        
        Args:
            user_id: 用户ID
            
        Returns:
            str: 用户数据文件夹路径
        """
        from config.settings import app_settings
        
        user_folder = os.path.join(app_settings.paths.DATA_FOLDER, user_id)
        if not os.path.exists(user_folder):
            os.makedirs(user_folder)
        
        # 创建子文件夹
        subfolders = ['teams', 'photos', 'exports']
        for subfolder in subfolders:
            subfolder_path = os.path.join(user_folder, subfolder)
            if not os.path.exists(subfolder_path):
                os.makedirs(subfolder_path)
        
        return user_folder
    
    def get_user_data_path(self, user_id: str, data_type: str = "") -> str:
        """
        获取用户数据路径
        
        Args:
            user_id: 用户ID
            data_type: 数据类型 (teams, photos, exports)
            
        Returns:
            str: 数据路径
        """
        from config.settings import app_settings
        
        user_folder = os.path.join(app_settings.paths.DATA_FOLDER, user_id)
        
        if data_type:
            return os.path.join(user_folder, data_type)
        else:
            return user_folder
    
    def is_new_user(self, user_id: str) -> bool:
        """
        检查是否为新用户
        
        Args:
            user_id: 用户ID
            
        Returns:
            bool: 是否为新用户
        """
        user_folder = self.get_user_data_path(user_id)
        teams_folder = os.path.join(user_folder, 'teams')
        
        # 检查是否有球队数据
        if not os.path.exists(teams_folder):
            return True
        
        # 检查是否有球队文件
        team_files = [f for f in os.listdir(teams_folder) if f.endswith('.json')]
        return len(team_files) == 0

    def _save_auth_to_storage(self, user_id: str, password_hint: str, login_time: float) -> None:
        """保存认证状态到URL参数"""
        # 生成认证token
        auth_token = f"{user_id}_{login_time}"

        # 更新URL参数
        st.query_params['auth_token'] = auth_token

    def _restore_auth_from_storage(self) -> None:
        """从URL参数恢复认证状态"""
        # 如果已经有有效的session，不需要恢复
        if ('user_id' in st.session_state and
            'login_time' in st.session_state):
            return

        # 检查URL参数中是否有认证信息
        query_params = st.query_params
        if 'auth_token' in query_params:
            try:
                # 解析认证token
                auth_token = query_params['auth_token']
                # token格式: user_id_timestamp
                parts = auth_token.split('_')
                if len(parts) >= 3:  # user_xxx_timestamp
                    user_id = '_'.join(parts[:-1])  # 重新组合user_id
                    login_time_str = parts[-1]      # 最后一部分是时间戳
                    login_time = float(login_time_str)

                    # 检查是否过期
                    if time.time() - login_time < self.session_timeout:
                        # 恢复session state
                        st.session_state.user_id = user_id
                        st.session_state.login_time = login_time

                        # 重新生成密码提示（简化版）
                        st.session_state.user_password_hint = f"{user_id[:3]}***"

            except (ValueError, IndexError):
                # 无效的token，忽略
                pass

    def _clear_auth_storage(self) -> None:
        """清除URL参数中的认证数据"""
        if 'auth_token' in st.query_params:
            del st.query_params['auth_token']
